{"name": "document-importer", "version": "1.0.0", "description": "Script to import Salesforce documents into PostgreSQL", "main": "import-documents.js", "scripts": {"start": "ts-node main.ts", "start:shared": "ts-node shared.ts", "start:download": "ts-node ./download.ts", "start:download-delta": "ts-node ./download-delta.ts", "build": "tsc"}, "dependencies": {"cli-progress": "^3.12.0", "dotenv": "^16.3.1", "jsforce": "^1.11.1", "mime-types": "^2.1.35", "pg": "^8.11.3", "winston": "^3.17.0"}, "devDependencies": {"@types/cli-progress": "^3.11.5", "@types/jsforce": "^1.11.0", "@types/mime-types": "^2.1.4", "@types/node": "^20.10.0", "@types/pg": "^8.10.9", "ts-node": "^10.9.1", "typescript": "^5.3.2"}}