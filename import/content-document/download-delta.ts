/**
 * Content Document Delta Download Script
 *
 * This script finds documents that exist in Salesforce but not in the PostgreSQL database
 * and downloads them for import.
 *
 * It will:
 * 1. Get all Document IDs from PostgreSQL
 * 2. Get all Candidate ContentDocument IDs from Salesforce
 * 3. Compare counts and find delta (candidate documents in Salesforce but not in PostgreSQL)
 * 4. Download the delta candidate documents with full details
 * 5. Download ALL job application content documents (same as original download.ts)
 * 6. Save results to separate files: candidate-content-document-link.json and job-application-content-document-link.json
 */

import jsforce from 'jsforce'
import { Pool } from 'pg'
import { config as dotenvConfig } from 'dotenv'
import { logger } from './logger'
import { writeFileSync } from 'fs'
import * as path from 'path'
import { createSalesforceConnection } from './download'
import { fetchJobApplicationContentDocuments } from './utils/download'
import { batchQuery } from './utils/batch-query'

dotenvConfig()

// Database connection configuration
const dbConfig = {
  host: process.env.PG_HOST || 'localhost',
  port: parseInt(process.env.PG_PORT || '5432', 10),
  database: process.env.PG_DATABASE || 'Test_Environment',
  user: process.env.PG_USERNAME || 'postgres',
  password: process.env.PG_PASSWORD || 'postgres',
  schema: process.env.PG_SCHEMA || 'public'
}

// Salesforce connection configuration
const sfConfig = {
  loginUrl: process.env.SF_LOGIN_URL || 'https://test.salesforce.com',
  url: process.env.SF_URL || '',
  clientId: process.env.SF_CLIENT_ID || '',
  clientSecret: process.env.SF_CLIENT_SECRET || '',
  username: process.env.SF_USERNAME || '',
  password: process.env.SF_PASSWORD || ''
}

main()

async function main() {
  try {
    logger.log('Starting Content Document Delta Download...')

    // Step 1: Get all Document IDs from PostgreSQL
    logger.log('Step 1: Connecting to PostgreSQL and fetching Document IDs...')
    const pool = new Pool(dbConfig)
    const postgresDocumentIds = await getPostgresDocumentIds(pool)
    logger.log(`Found ${postgresDocumentIds.length} documents in PostgreSQL`)

    // Step 2: Get all ContentDocument IDs from Salesforce
    logger.log('Step 2: Connecting to Salesforce and fetching ContentDocument IDs...')
    const conn = await createSalesforceConnection(sfConfig)
    const salesforceDocumentIds = await getSalesforceDocumentIds(conn)
    logger.log(`Found ${salesforceDocumentIds.length} documents in Salesforce`)

    // Step 3: Compare counts
    logger.log('Step 3: Comparing document counts...')
    if (postgresDocumentIds.length === salesforceDocumentIds.length) {
      logger.log('Document counts match. No delta to process.')
      await pool.end()
      return
    }

    logger.log(`Count mismatch detected:`)
    logger.log(`  PostgreSQL: ${postgresDocumentIds.length} documents`)
    logger.log(`  Salesforce: ${salesforceDocumentIds.length} documents`)
    logger.log(`  Difference: ${salesforceDocumentIds.length - postgresDocumentIds.length} documents`)

    // Step 4: Find delta (documents in Salesforce but not in PostgreSQL)
    logger.log('Step 4: Finding delta documents...')
    const postgresIdSet = new Set(postgresDocumentIds)
    const salesforceIdSet = new Set(salesforceDocumentIds)

    const deltaDocumentIds = salesforceDocumentIds.filter(id => !postgresIdSet.has(id))
    const postgresOnlyIds = postgresDocumentIds.filter(id => !salesforceIdSet.has(id))

    logger.log(`Found ${deltaDocumentIds.length} documents in Salesforce that are missing from PostgreSQL`)
    logger.log(`Found ${postgresOnlyIds.length} documents in PostgreSQL that are not in Salesforce candidate documents`)
    logger.log(`Math check: ${postgresDocumentIds.length} (PG) + ${deltaDocumentIds.length} (SF only) - ${postgresOnlyIds.length} (PG only) = ${postgresDocumentIds.length + deltaDocumentIds.length - postgresOnlyIds.length} (should equal ${salesforceDocumentIds.length})`)

    if (deltaDocumentIds.length === 0) {
      logger.log('No delta documents to download.')
      await pool.end()
      return
    }

    // Step 5: Download delta documents with full details
    logger.log('Step 5: Downloading delta documents with full details...')
    const deltaDocuments = await downloadDeltaDocuments(conn, deltaDocumentIds)

    // Step 6: Save candidate delta documents to JSON file
    logger.log('Step 6: Saving candidate delta documents to file...')
    const dataDir = path.join(__dirname, 'data-for-import')
    const candidateOutputPath = path.join(dataDir, 'candidate-content-document-link.json')

    writeFileSync(candidateOutputPath, JSON.stringify(deltaDocuments, null, 2))

    logger.log(`Successfully saved ${deltaDocuments.length} candidate delta documents to ${candidateOutputPath}`)

    // Step 7: Download ALL job application content documents (same as original download.ts)
    logger.log('Step 7: Downloading ALL job application content documents...')
    const jobApplicationContentDocumentResult = await fetchJobApplicationContentDocuments(conn)

    const jobAppOutputPath = path.join(dataDir, 'job-application-content-document-link.json')
    writeFileSync(jobAppOutputPath, JSON.stringify(jobApplicationContentDocumentResult, null, 2))

    logger.log(`Successfully saved ${jobApplicationContentDocumentResult.length} job application content documents to ${jobAppOutputPath}`)
    logger.log('Content Document Delta Download completed successfully!')

    // Close connections
    await pool.end()
    logger.log('Database connection closed')

  } catch (error) {
    logger.error('Error during delta download:', error)
    process.exit(1)
  }
}

/**
 * Get all Document IDs from PostgreSQL
 */
async function getPostgresDocumentIds(pool: Pool): Promise<string[]> {
  try {
    const query = 'SELECT "Id" FROM public."Document"'
    const result = await pool.query(query)
    return result.rows.map(row => row.Id)
  } catch (error) {
    logger.error('Error fetching PostgreSQL document IDs:', error)
    throw error
  }
}

/**
 * Get all ContentDocument IDs from Salesforce
 */
async function getSalesforceDocumentIds(conn: jsforce.Connection): Promise<string[]> {
  try {
    const query = `
      SELECT ContentDocument.Id
      FROM ContentDocumentLink
      WHERE LinkedEntityId IN (SELECT id from emmy__e_Candidate__c)
      AND ContentDocument.FileType != 'SNOTE'
    `

    const records = await queryAll(conn, query)
    const allIds = records.map(record => record.ContentDocument.Id)

    // Deduplicate the IDs since the same document can be linked to multiple candidates
    const uniqueIds = [...new Set(allIds)]

    logger.log(`Found ${allIds.length} total ContentDocumentLink records, ${uniqueIds.length} unique ContentDocument IDs`)

    return uniqueIds
  } catch (error) {
    logger.error('Error fetching Salesforce document IDs:', error)
    throw error
  }
}

/**
 * Download delta documents with full details
 */
async function downloadDeltaDocuments(conn: jsforce.Connection, deltaDocumentIds: string[]): Promise<any[]> {
  try {
    // Create IN clause with the delta document IDs
    const inClause = deltaDocumentIds.map(id => `'${id}'`).join(', ')

    const query = `
      SELECT ContentDocument.Id, ContentDocument.Title,
        ContentDocument.LatestPublishedVersion.Id,
        ContentDocument.LatestPublishedVersion.PathOnClient,
        ContentDocument.LatestPublishedVersion.VersionData,
        ContentDocument.LatestPublishedVersion.Description,
        ContentDocument.LatestPublishedVersion.VersionNumber,
        LinkedEntityId, Visibility
      FROM ContentDocumentLink
      WHERE LinkedEntityId IN (SELECT id from emmy__e_Candidate__c)
      AND ContentDocument.FileType != 'SNOTE'
      AND ContentDocument.Id IN (${inClause})
    `

    logger.log(`Executing query for ${deltaDocumentIds.length} delta documents...`)
    const records = await queryAll(conn, query)
    logger.log(`Downloaded details for ${records.length} delta documents`)

    return records
  } catch (error) {
    logger.error('Error downloading delta documents:', error)
    throw error
  }
}

/**
 * Query all records using batch processing
 */
async function queryAll(conn: jsforce.Connection, soql: string): Promise<any[]> {
  let records: any[] = []
  let i = 0

  for await (const batch of batchQuery(conn, soql, 'batch')) {
    logger.log(
      `Fetched ${batch.length} records from batch ${i++} (${
        records.length
      } total)`
    )
    records = records.concat(batch)
  }

  return records
}
