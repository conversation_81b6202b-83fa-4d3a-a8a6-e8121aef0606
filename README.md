# Emmysoft Connector

Working Connections Salesforce → Postgres:

- [x] Account
- [x] Candidate
- [x] Contact
- [x] JobApplication
- [x] Step
- [x] Project
- [x] Tickets
- [x] Comments
- [ ] EmmyUsers (API)
- [ ] Documents (API)

Ugly Commits:

- Everything up until the _Fix Condition_ [
  `6f95ec9`](https://github.com/EmmySoft-GmbH/emmysoft-connector/commit/6f95ec9b6b506088a5e07277297e2d39982ccbde) commit
  was quite clean. After that I have enabled a bulk deletion, which messes up the amount of API request made to the
  Salesforce API and brakes the progress bar visualization.

## Running ContentDocument Import Script

The project includes a dedicated script for synchronizing ContentDocuments from Salesforce to PostgreSQL. This script
handles downloading file content, comparing versions, and updating the database accordingly.

### Prerequisites

1. Navigate to the import script directory:

   ```bash
   cd import/content-document
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Create a `.env` file based on the provided `.env.example`:

   ```bash
   cp .env.example .env
   ```

4. Edit the `.env` file with your Salesforce and PostgreSQL credentials:

   ```
   # PostgreSQL Database Configuration
   PG_HOST="localhost"
   PG_PORT=5432
   PG_USERNAME="postgres"
   PG_PASSWORD="your_password"
   PG_DATABASE="your_database"
   PG_SCHEMA="public"

   # Salesforce Configuration
   SF_LOGIN_URL="https://test.salesforce.com" # or https://login.salesforce.com for production
   SF_URL="your_salesforce_instance_url"
   SF_CLIENT_ID="your_client_id"
   SF_CLIENT_SECRET="your_client_secret"
   SF_USERNAME="your_username"
   SF_PASSWORD="your_password_with_security_token"
   ```

### Preparing Import Data

You have two options to prepare the import data:

#### Option 1: Use the Download Script

1. **Full Download** - Download all ContentDocumentLink records from Salesforce:

   ```bash
   npm run start:download
   ```

2. **Delta Download** - Download only documents missing from PostgreSQL:

   ```bash
   npm run start:download-delta
   ```

   The delta script will:
   - Compare candidate documents between Salesforce and PostgreSQL
   - Download only missing candidate documents (delta)
   - Download ALL job application documents (same as full download)
   - Save to separate files: `candidate-content-document-link-delta.json` and `job-application-content-document-link.json`

   Both scripts will:
   - Connect to Salesforce using your credentials from the `.env` file
   - Automatically exclude SNOTE files from candidate queries
   - Save results to the `data-for-import` directory

#### Option 2: Manual Export from Salesforce

1. Create a JSON file containing ContentDocumentLink records (all files attached to Candidates) from Salesforce:
  - Place the file at `import/content-document/data-for-import/candidate-content-document-link.json`
  - The file should contain an array of records with ContentDocument, LatestPublishedVersion, and LinkedEntityId
    information
  - You can download this data directly from Salesforce by running the following SOQL query in Salesforce Data Export or
    Developer Console:
    ```sql
    SELECT ContentDocument.Id, ContentDocument.Title,
           ContentDocument.LatestPublishedVersion.Id,
           ContentDocument.LatestPublishedVersion.PathOnClient,
           ContentDocument.LatestPublishedVersion.FileType,
           ContentDocument.LatestPublishedVersion.VersionData,
           ContentDocument.LatestPublishedVersion.Description,
           ContentDocument.LatestPublishedVersion.VersionNumber,
           LinkedEntityId, Visibility
    FROM ContentDocumentLink
    WHERE LinkedEntityId IN (SELECT id from emmy__e_Candidate__c) AND ContentDocument.FileType != 'SNOTE'
    ```
  - Export the query results as JSON and save to the data-for-import directory as `candidate-content-document-link.json`

### Running the Import

1. Start the import process:

   ```bash
   npm start
   ```

2. The script will:

  - Connect to Salesforce and PostgreSQL
  - Check if each document's candidate exists in the database
  - Compare version numbers between Salesforce and the database
  - Download file content from Salesforce when needed using the VersionData blob API
  - Create or update document records in the PostgreSQL database
  - Track progress with a CLI progress bar
  - Log detailed information about each processed document

3. The import process can be resumed if interrupted, as it saves the state of the last processed document.

### Updating Shared Status for Job Application Documents

After running the main import script, you can use the shared.ts script to update the shared status for documents linked
to job applications.

1. If you used the download script (`npm run start:download`), the job application ContentDocumentLink records have already been downloaded to `import/content-document/data-for-import/job-application-content-document-link.json`.

2. If you need to manually create this file:

  - Place the file at `import/content-document/data-for-import/job-application-content-document-link.json`
  - You can download this data directly from Salesforce by running the following SOQL query:
    ```sql
    SELECT ContentDocument.Id, LinkedEntityId
    FROM ContentDocumentLink
    WHERE LinkedEntityId IN (SELECT id from emmy__e_Job_Application__c)
    ```
  - Export the query results as JSON and save to the data-for-import directory

3. Run the shared status update script:

   ```bash
   npm run start:shared
   ```

3. The script will:
  - Connect to PostgreSQL
  - For each ContentDocument ID in the JSON file, update the shared field to true in the Document table
  - Track progress with a CLI progress bar
  - Log detailed information about each processed document

**Note:** This script should be run after the main import script has completed to ensure all documents are properly
imported before updating their shared status.

### Import Process Details

- Documents are only downloaded if they are new or have a newer version in Salesforce
- When a document has an unchanged version, only the title and version number are updated without downloading the file
  content again
- The script handles version number comparison correctly between Salesforce's "VersionNumber" and the database's "
  versionNumber" fields
- Documents with FileType === 'SNOTE' are automatically skipped during processing (both in the download script and the import script)
- The script logs detailed information about skipped SNOTE files and includes them in the final statistics

## Custom Connector Processing

The Emmysoft Connector now supports custom data processing between source retrieval and target saving. This allows you
to implement custom logic for specific connectors, such as fetching additional data or transforming records before they
are saved to the target database.

### Basic Usage

The simplest way to add custom processing to a connector is to use the `ConnectorFactory`:

```typescript
import ConnectorFactory from '@/models/ConnectorFactory'
import DataRecord from '@/models/DataRecord'

// Create a connector with custom processing
const connector = ConnectorFactory.createWithCustomProcessing(
  fromPath,
  toPath,
  toIdColumn,
  mappings,
  async (record: DataRecord) => {
    // Custom processing logic here
    // For example, fetch additional data or transform the record
    record.payload.customField = 'Custom value'
    return record
  }
)
```

### Advanced Usage with ConnectorExtensions

For more complex scenarios, you can use the `ConnectorExtensions` framework which provides a more flexible and reusable
approach:

```typescript
import {
  BaseDataProcessor,
  ConnectorBuilder
} from '@/models/ConnectorExtensions'
import DataRecord from '@/models/DataRecord'

// Create a custom processor
class CustomDataProcessor extends BaseDataProcessor {
  constructor() {
    super('CustomDataProcessor')
  }

  protected async processInternal(record: DataRecord): Promise<DataRecord> {
    // Custom processing logic
    record.payload.processedBy = 'CustomDataProcessor'
    return record
  }
}

// Create a connector with multiple processors
const connector = ConnectorBuilder.fromSimpleParameters(
  fromDataSource,
  fromTable,
  fromIdColumn,
  toDataSource,
  toTable,
  toIdColumn,
  mappings
)
  .withName('Custom Connector')
  .withProcessor(new CustomDataProcessor())
  .withProcessingFunction('AdditionalProcessor', async record => {
    record.payload.additionalField = 'Additional value'
    return record
  })
  .build()
```

### Example: Salesforce File Connector with Additional Data

Here's an example of a Salesforce file connector that fetches additional data from ContentVersion using jsforce:

```typescript
import { ConnectorBuilder } from '@/models/ConnectorExtensions'
import SalesforceDataSource from '@/datasources/salesforce.datasource'

export default function EnhancedFileConnector(
  from: DataSource,
  to: DataSource
): Connector {
  return ConnectorBuilder.fromSimpleParameters(
    from,
    'ContentDocument',
    'Id',
    to,
    'Document',
    'Id',
    mappings
  )
    .withName('Enhanced File Connector')
    .withProcessingFunction('ContentVersionFetcher', async record => {
      if (!(record.path.dataSource instanceof SalesforceDataSource)) {
        return record
      }

      const salesforceDS = record.path.dataSource as SalesforceDataSource

      if (record.payload.LatestPublishedVersionId) {
        const versionId = record.payload.LatestPublishedVersionId as string

        // Use jsforce to fetch additional data
        const contentVersion = await salesforceDS.connection
          .sobject('ContentVersion')
          .retrieve(versionId)

        if (contentVersion) {
          // Add the additional data to the record
          record.payload['VersionData'] = contentVersion.VersionData
          record.payload['FileType'] = contentVersion.FileType
          record.payload['Title'] = contentVersion.Title
        }
      }

      return record
    })
    .build()
}
```

For more examples, see the sample implementations in `src/connectors/salesforce/contentDocument.connector.ts`.
